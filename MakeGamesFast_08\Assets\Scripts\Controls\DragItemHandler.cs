using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.InputSystem;
public class DragItemHandler : MonoBehaviour
{
    private bool isDragging = false;
    private Vector2 mouseStartPosition;
    private Vector2 dragItemStartPosition;
    private RectTransform draggedItem;

    private void OnEnable()
    {
        MouseInputHandler.OnMouseClickDown += Clicked;
        MouseInputHandler.OnMouseClickUp += Released;
        MouseInputHandler.OnMouseMoved += Drag;
    }

    private void OnDisable()
    {
        MouseInputHandler.OnMouseClickDown -= Clicked;
        MouseInputHandler.OnMouseClickUp -= Released;
        MouseInputHandler.OnMouseMoved -= Drag;
    }

    private void Clicked(GameObject hitObject)
    {
        if (hitObject == null) return;
        if (hitObject.TryGetComponent<Item>(out Item dragItem))
        {
            isDragging = true;
            draggedItem = dragItem.transform as RectTransform;
            mouseStartPosition = Mouse.current.position.ReadValue();
            dragItemStartPosition = draggedItem.position;
        }
    }
    private void Released(GameObject hitObject)
    {
        if (draggedItem == null) return;

        List<InventorySlot> slots = FindObjectsByType<InventorySlot>(FindObjectsSortMode.None).ToList();
        //List<InventoryWhiteboard> whiteboards = FindObjectsByType<InventoryWhiteboard>(FindObjectsSortMode.None).ToList();

        // Find all slots that intersect with the dragged item
        List<InventorySlot> intersectingSlots = new();
        foreach (InventorySlot slot in slots)
        {
            RectTransform slotRect = slot.transform as RectTransform;
            if (InventoryPlacementUtility.DoRectTransformsIntersect(draggedItem, slotRect))
            {
                intersectingSlots.Add(slot);
            }
        }

        // Find the best fitting slots for the dragged item
        if (draggedItem.TryGetComponent(out Item draggedItemComponent))
        {
            List<InventorySlot> bestFittingSlots = InventoryPlacementUtility.FindBestFittingSlots(intersectingSlots, slots, draggedItemComponent);
            if (bestFittingSlots.Count > 0)
            {
                Debug.Log($"Best fitting placement found for item size ({draggedItemComponent.Size.x}, {draggedItemComponent.Size.y})");
                Debug.Log($"Item would occupy {bestFittingSlots.Count} slots:");
                foreach (InventorySlot slot in bestFittingSlots)
                {
                    Debug.Log($"  - Slot at ({slot.X}, {slot.Y})");
                }
            }
            else
            {
                Debug.Log($"No valid placement found for item size ({draggedItemComponent.Size.x}, {draggedItemComponent.Size.y})");
            }
        }

        // Debug output to show which slots are intersecting
        Debug.Log($"Dragged item intersects with {intersectingSlots.Count} slots:");
        foreach (InventorySlot slot in intersectingSlots)
        {
            Debug.Log($"  - Slot at ({slot.X}, {slot.Y})");
        }

        isDragging = false;
        draggedItem = null;
        mouseStartPosition = Vector2.zero;
        dragItemStartPosition = Vector2.zero;
    }
    private void Drag(Vector2 position)
    {
        if (!isDragging) return;
        Vector2 mousePosition = Mouse.current.position.ReadValue();
        draggedItem.position = dragItemStartPosition + (mousePosition - mouseStartPosition);
    }


}