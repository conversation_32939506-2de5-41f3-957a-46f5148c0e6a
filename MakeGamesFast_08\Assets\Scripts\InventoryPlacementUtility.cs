using System.Collections.Generic;
using UnityEngine;

public static class InventoryPlacementUtility
{
    /// <summary>
    /// Checks if two RectTransforms intersect in any way (overlapping, touching, one inside the other)
    /// </summary>
    /// <param name="rect1">First RectTransform</param>
    /// <param name="rect2">Second RectTransform</param>
    /// <returns>True if the RectTransforms intersect, false otherwise</returns>
    public static bool DoRectTransformsIntersect(RectTransform rect1, RectTransform rect2)
    {
        if (rect1 == null || rect2 == null) return false;

        // Get world space corners for both RectTransforms
        Vector3[] corners1 = new Vector3[4];
        Vector3[] corners2 = new Vector3[4];

        rect1.GetWorldCorners(corners1);
        rect2.GetWorldCorners(corners2);

        // Convert to screen space bounds for easier calculation
        // corners are in order: bottom-left, top-left, top-right, bottom-right
        float rect1MinX = corners1[0].x;
        float rect1MaxX = corners1[2].x;
        float rect1MinY = corners1[0].y;
        float rect1MaxY = corners1[1].y;

        float rect2MinX = corners2[0].x;
        float rect2MaxX = corners2[2].x;
        float rect2MinY = corners2[0].y;
        float rect2MaxY = corners2[1].y;

        // Check for intersection using AABB (Axis-Aligned Bounding Box) collision detection
        // Two rectangles intersect if they overlap on both X and Y axes
        bool xOverlap = rect1MinX <= rect2MaxX && rect1MaxX >= rect2MinX;
        bool yOverlap = rect1MinY <= rect2MaxY && rect1MaxY >= rect2MinY;

        return xOverlap && yOverlap;
    }

    public static List<InventorySlot> FindBestFittingSlots(List<InventorySlot> intersectingSlots, Item item)
    {

    }

}